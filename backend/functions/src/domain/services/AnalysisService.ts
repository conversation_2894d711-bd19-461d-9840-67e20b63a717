import { BasePigeon } from "../value-objects/BasePigeon";

export interface RankedBasePigeon {
    basePigeon: BasePigeon;
    // basePigeon: SimpleAnalysis;
    score: number; // Higher score means better match
}

export interface AnalysisService {
    /**
     * Analyzes a pigeon picture and returns an array of BasePigeons ranked by closeness to the image
     * @param storageFilePath Path to the image in storage
     * @returns Promise resolving to an array of RankedBasePigeon objects sorted by score (highest first)
     */
    analyzePigeonPicture(storageFilePath: string): Promise<RankedBasePigeon[]>;
}
