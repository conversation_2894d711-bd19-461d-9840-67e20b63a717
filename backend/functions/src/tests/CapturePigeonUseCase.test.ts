/* eslint-disable @typescript-eslint/no-unused-vars */
import { Pigeon } from "../domain/entities/Pigeon";
import { defaultTrainer, Trainer, TrainerDocument } from "../domain/entities/Trainer";
import { PigeonGang } from "../domain/enums/PigeonGang";
import { PigeonRepository } from "../domain/repositories/PigeonRepository";
import { TrainerRepository } from "../domain/repositories/TrainerRepository";
import { AnalysisService, RankedBasePigeon } from "../domain/services/AnalysisService";
import { Coordinates, GeoLocationService } from "../domain/services/GeoLocationService";
import { CapturePigeonUseCase } from "../use-cases/CapturePigeonUseCase";

class FakeTrainerRepository implements TrainerRepository {
    private trainers: { [id: string]: Trainer } = {};

    async getById(id: string): Promise<Trainer | null> {
        return this.trainers[id] || null;
    }

    async update(trainer: TrainerDocument): Promise<void> {
        this.trainers[trainer.id] = new Trainer({ ...trainer, decks: [] });
    }

    add(trainer: Trainer) {
        this.trainers[trainer.id] = trainer;
    }
}

class FakePigeonRepository implements PigeonRepository {
    public pigeons: { [id: string]: Pigeon } = {};

    async save(pigeon: Pigeon): Promise<void> {
        this.pigeons[pigeon.id] = pigeon;
    }

    async getByOwnerId(ownerId: string): Promise<Pigeon[]> {
        return Object.values(this.pigeons).filter((pigeon) => pigeon.ownerId === ownerId);
    }
}

class FakeAnalysisService implements AnalysisService {
    analyzePigeonPicture(storageFilePath: string): Promise<RankedBasePigeon[]> {
        return Promise.resolve([]);
    }
}

class FakeGeoLocationService implements GeoLocationService {
    determineGang(coordinates: Coordinates, date?: Date): PigeonGang {
        // For testing, use a deterministic approach based on coordinates
        const { latitude, longitude } = coordinates;

        // Use a simple rule for testing
        if (latitude > 0 && longitude > 0) {
            return PigeonGang.ONE;
        } else if (latitude > 0 && longitude <= 0) {
            return PigeonGang.TWO;
        } else if (latitude <= 0 && longitude <= 0) {
            return PigeonGang.THREE;
        } else if (latitude <= 0 && longitude > 0) {
            return PigeonGang.FOUR;
        }

        // Default
        return PigeonGang.ONE;
    }
}

describe("CapturePigeonWithAnalysisUseCase", () => {
    const fakeTrainerRepo = new FakeTrainerRepository();
    const fakePigeonRepo = new FakePigeonRepository();
    const analysisService = new FakeAnalysisService();
    const geoLocationService = new FakeGeoLocationService();
    const capturePigeonUseCase = new CapturePigeonUseCase(
        fakeTrainerRepo,
        fakePigeonRepo,
        analysisService,
        geoLocationService,
    );

    it("should throw if trainer not found", async () => {
        const filePath = "shots/unknownTrainerId/40.7128_-74.0060_file.jpg";
        await expect(capturePigeonUseCase.execute(filePath)).rejects.toThrow("Trainer not found");
    });

    it("should throw if no pigeon balls", async () => {
        const trainer = new Trainer({ ...defaultTrainer, id: "noBallTrainerId", pigeonBalls: 0 });
        const filePath = "shots/noBallTrainerId/40.7128_-74.0060_file.jpg";
        fakeTrainerRepo.add(trainer);
        await expect(capturePigeonUseCase.execute(filePath)).rejects.toThrow("No capture stock available");
    });

    it("should capture a pigeon with invalid coordinates", async () => {
        const trainerId = "oneBallTrainerId";
        const trainer = new Trainer({ ...defaultTrainer, id: trainerId, pigeonBalls: 1 });
        const filePath = `shots/${trainerId}/invalid_coords_file.jpg`;
        fakeTrainerRepo.add(trainer);
        await capturePigeonUseCase.execute(filePath);
        const pigeonList = Object.values(fakePigeonRepo.pigeons);
        const capturedPigeon = pigeonList.find((p) => p.ownerId === trainerId);
        expect(capturedPigeon).toBeDefined();
        expect(capturedPigeon?.ownerId).toBe(trainerId);
        expect(capturedPigeon?.gang).toBe(PigeonGang.ONE); // Default gang when no coordinates
        const updatedTrainer = await fakeTrainerRepo.getById(trainerId);
        expect(updatedTrainer?.pigeonBalls).toBe(0);
    });

    it("should capture a pigeon with valid coordinates", async () => {
        const trainerId = "twoBallTrainerId";
        const trainer = new Trainer({ ...defaultTrainer, id: trainerId, pigeonBalls: 1 });
        // Coordinates in the filename (positive lat, negative long - should be Gang TWO in our test)
        const filePath = `shots/${trainerId}/40.7128_-74.0060_file.jpg`;
        fakeTrainerRepo.add(trainer);
        await capturePigeonUseCase.execute(filePath);
        const pigeonList = Object.values(fakePigeonRepo.pigeons);
        const capturedPigeon = pigeonList.find((p) => p.ownerId === trainerId);
        expect(capturedPigeon).toBeDefined();
        expect(capturedPigeon?.ownerId).toBe(trainerId);
        expect(capturedPigeon?.gang).toBe(PigeonGang.TWO); // Based on our test coordinates
        const updatedTrainer = await fakeTrainerRepo.getById(trainerId);
        expect(updatedTrainer?.pigeonBalls).toBe(0);
    });
});
