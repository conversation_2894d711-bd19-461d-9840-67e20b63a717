/**
 * Import function triggers from their respective submodules:
 *
 * import {onCall} from "firebase-functions/v2/https";
 * import {onDocumentWritten} from "firebase-functions/v2/firestore";
 *
 * See a full list of supported triggers at https://firebase.google.com/docs/functions
 */

import * as dotenv from "dotenv";
import * as logger from "firebase-functions/logger";
import { onCall } from "firebase-functions/v2/https";
import { onObjectFinalized } from "firebase-functions/v2/storage";
import path from "path";
dotenv.config({ path: path.resolve(__dirname, "../.env") });

import * as admin from "firebase-admin";
admin.initializeApp();

import { FirestoreBasePigeonRepository } from "./infrastructure/firebase/FirestoreBasePigeonRepository";
import { FirestorePigeonRepository } from "./infrastructure/firebase/FirestorePigeonRepository";
import { FirestoreTrainerRepository } from "./infrastructure/firebase/FirestoreTrainerRepository";
import { AdvancedGeoLocationService } from "./infrastructure/geolocation/AdvancedGeoLocationService";
import { OpenAiServiceV2 } from "./infrastructure/openai/OpenAiServiceV2";
import { CapturePigeonUseCase } from "./use-cases/CapturePigeonUseCase";
import { GetTrainerPigeonsUseCase } from "./use-cases/GetTrainerPigeonsUseCase";

// Initialisation de l'admin SDK Firebase

const trainerRepo = new FirestoreTrainerRepository();
const pigeonRepo = new FirestorePigeonRepository();
const basePigeonRepo = new FirestoreBasePigeonRepository();

// Original implementation with OpenAiAiService and CapturePigeonUseCase
const aiService = new OpenAiServiceV2(basePigeonRepo);
const capturePigeonUseCase = new CapturePigeonUseCase(
    trainerRepo,
    pigeonRepo,
    aiService,
    new AdvancedGeoLocationService(),
);

const getTrainerPigeonsUseCase = new GetTrainerPigeonsUseCase(trainerRepo, pigeonRepo);

// export const capturePigeon = onRequest(async (req, res) => {
//     try {
//         const {trainerId, pigeonData} = req.body;
//         const newPigeon = await capturePigeonUseCase.execute(trainerId, pigeonData);
//         res.status(200).json(newPigeon);
//     } catch (error: any) {
//         res.status(400).json({error: error.message});
//     }
// });

export const onFileUpload = onObjectFinalized(
    {
        region: "us-east1",
        bucket: "pigeon-gogo.firebasestorage.app",
    },
    async (object) => {
        const { data } = object;
        const filePath = data.name;

        if (filePath.startsWith("shots")) {
            // File path should be in format: shots/<trainerId>/<latitude>_<longitude>_<fileName>.jpg
            logger.info(`Processing file upload: ${filePath}`);

            capturePigeonUseCase.execute(filePath).catch((error: unknown) => {
                logger.error("Error processing file upload:", error);
                return;
            });
            return;
        }
        return;
    },
);

/**
 * HTTP function to get a trainer's pigeondex (all pigeons owned by the trainer)
 * Requires authentication - the user can only access their own pigeondex
 */
export const getTrainerPigeondex = onCall(
    {
        region: "us-east1",
    },
    async (request) => {
        try {
            if (!request.auth) {
                throw new Error("Unauthorized - Authentication required");
            }

            const trainerId = request.auth.uid;
            logger.info(`Getting pigeondex for trainer: ${trainerId}`);

            const pigeondex = await getTrainerPigeonsUseCase.execute(trainerId);
            return pigeondex;
        } catch (error: unknown) {
            logger.error("Error getting trainer pigeondex:", error);
            throw error;
        }
    },
);
