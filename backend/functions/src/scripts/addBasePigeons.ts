import * as dotenv from "dotenv";
import * as admin from "firebase-admin";
import * as path from "path";
dotenv.config({ path: path.resolve(__dirname, "../../.env") });

import { v4 as uuidv4 } from "uuid";
import { BirdType } from "../domain/enums/BirdType";
import { PigeonDistinctiveness } from "../domain/enums/PigeonDistinctiveness";
import { BasePigeon } from "../domain/value-objects/BasePigeon";
import serviceAccount from "../firebaseServiceAccount";
import { FirestoreBasePigeonRepository } from "../infrastructure/firebase/FirestoreBasePigeonRepository";

// Initialize Firebase Admin SDK
admin.initializeApp({
    credential: admin.credential.cert(serviceAccount as admin.ServiceAccount),
});

const basePigeons: BasePigeon[] = [
    {
        id: uuidv4(),
        name: {
            fr: "<PERSON>ise<PERSON>",
            en: "Biset",
        },
        story: {
            fr: "Un pigeon classique de la ville",
            en: "A standard city pigeon",
        },
        slug: "biset",
        skin: {
            smallThumbnailUrl: "https://storage.googleapis.com/pigeon-gogo.appspot.com/base-pigeons/biset_thumb.jpg",
            originalUrl: "https://storage.googleapis.com/pigeon-gogo.appspot.com/base-pigeons/biset.jpg",
        },
        typicalAnalysis: {
            isFake: 0, // 0 means not fake
            isBird: true,
            isDead: false,
            isABabyPigeon: false,
            birdType: BirdType.PIGEON,
            distinctiveness: PigeonDistinctiveness.COMMON,
            description:
                "A standard city pigeon with dark grey head, iridescent green neck, light grey wings with two black bars, and a dark grey tail.",
        },
    },
    // Add more base pigeons here...
];

async function addBasePigeons() {
    const repo = new FirestoreBasePigeonRepository();

    for (const pigeon of basePigeons) {
        try {
            await repo.add(pigeon);
            console.log(`Successfully added ${pigeon.name}`);
        } catch (error) {
            console.error(`Error adding ${pigeon.name}:`, error);
        }
    }

    await admin.app().delete();
    console.log("Finished adding base pigeons");
}

addBasePigeons();
