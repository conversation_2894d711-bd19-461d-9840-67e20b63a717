import * as dotenv from "dotenv";
import * as admin from "firebase-admin";
import * as path from "path";
dotenv.config({ path: path.resolve(__dirname, "../../.env") });

import serviceAccount from "../firebaseServiceAccount";
import { FirestoreBasePigeonRepository } from "../infrastructure/firebase/FirestoreBasePigeonRepository";
import { OpenAiServiceV2 } from "../infrastructure/openai/OpenAiServiceV2";
import { writeFile } from "fs/promises";

// Initialize Firebase Admin SDK
admin.initializeApp({
    credential: admin.credential.cert(serviceAccount as admin.ServiceAccount),
});

interface PigeonAnalysisResult {
    name: string;
    distinctiveness: string; // "COMMON", "UNUSUAL", "RARE"
    description: string;
}

/**
 * This script loops through all pigeon names in the pigeon-list file and uses OpenAI
 * to analyze the images stored in Firebase Storage under base/<pigeon_name>.jpg.
 * It extracts only the distinctiveness (COMMON, UNUSUAL, RARE) and description parts.
 */
async function analyzePigeonImages() {
    try {
        console.log("Starting pigeon image analysis...");

        // Initialize repositories and services
        const basePigeonRepo = new FirestoreBasePigeonRepository();
        const analysisService = new OpenAiServiceV2(basePigeonRepo);

        // Read pigeon names from file

        const slugList = [
            "angel",
            "anthony",
            "beau_garcon",
            "cassandra",
            "chanjon",
            "charmeur",
            "cordonnier",
            "crane_doeuf",
            "croquemort",
            "demineur",
            "double_face",
            "fischer",
            "frank",
            "jackie",
            "jacques",
            "john_doe",
            "l_eclaireur",
            "l_heritier",
            "l_ombre",
            "la_mouche",
            "le_cerveau",
            "le_fossoyeur",
            "le_rat",
            "lily",
            "lucky_duke",
            "moe",
            "monotone",
            "nina",
            "patte_blanche",
            "paulie",
            "pete_l_indecis",
            "pique_assiette",
            "rico",
            "rorschak",
            "rosetta",
            "rusty",
            "silvio",
            "spectre",
            "tony",
            "vinny",
            "vito",
        ];

        const results: PigeonAnalysisResult[] = [];

        // Process each pigeon
        for (const slug of slugList) {
            try {
                console.log(`Processing ${slug}`);

                const downloadableFirebaseUrl = (
                    await admin
                        .storage()
                        .bucket("gs://pigeon-gogo.firebasestorage.app")
                        .file(`base/${slug}.jpg`)
                        .getSignedUrl({ action: "read", expires: "11-11-2025" })
                )[0];

                // Call OpenAI to get the simple analysis (only original/standard and description)
                const analysis = await analysisService.getSimplePigeonAnalysis(downloadableFirebaseUrl);
                // const analysis = await analysisService.getSimplePigeonAnalysis(
                //     "https://firebasestorage.googleapis.com/v0/b/pigeon-gogo.firebasestorage.app/o/shots%2F6e71e118-9888-4872-8623-f46d5cb31ed4%2Fstandard_12.png?alt=media&token=9f38c6bc-b59a-4574-a2ce-5e69bf51092d",
                // );
                results.push({
                    name: slug,
                    distinctiveness: analysis.distinctiveness,
                    description: analysis.description,
                });

                console.log(`Analysis complete for ${analysis}:`);
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                console.error(`Error analyzing ${slug}:`, errorMessage);
            }
        }

        // Save results to a file
        const resultsPath = path.resolve(__dirname, "pigeon-analysis-results.json");
        await writeFile(resultsPath, JSON.stringify(results, null, 2), "utf8");

        console.log(`Analysis complete! Results saved to ${resultsPath}`);
    } catch (error) {
        console.error("Error in analyzePigeonImages:", error);
    } finally {
        // Close the Firebase connection
        await admin.app().delete();
    }
}

// Run the analysis
analyzePigeonImages()
    .then(() => {
        console.log("Script completed");
    })
    .catch((error) => {
        console.error("Script failed:", error);
    });
