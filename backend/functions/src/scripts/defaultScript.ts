/* eslint-disable max-len */
import * as dotenv from "dotenv";
import * as admin from "firebase-admin";
import * as path from "path";
import { FirestoreBasePigeonRepository } from "../infrastructure/firebase/FirestoreBasePigeonRepository";
dotenv.config({ path: path.resolve(__dirname, "../../.env") });

import serviceAccount from "../firebaseServiceAccount";
// import {defaultBasePigeon} from '../domain/value-objects/BasePigeon';
import { FirestorePigeonRepository } from "../infrastructure/firebase/FirestorePigeonRepository";
import { FirestoreTrainerRepository } from "../infrastructure/firebase/FirestoreTrainerRepository";
import { AdvancedGeoLocationService } from "../infrastructure/geolocation/AdvancedGeoLocationService";
import { OpenAiServiceV2 } from "../infrastructure/openai/OpenAiServiceV2";

// Initialisation de Firebase Admin SDK
admin.initializeApp({
    credential: admin.credential.cert(serviceAccount as admin.ServiceAccount),
});

async function testCapturePigeon() {
    const basePigeonRepo = new FirestoreBasePigeonRepository();
    const pigeonRepo = new FirestorePigeonRepository();
    const trainerRepo = new FirestoreTrainerRepository();
    // const aiService = new OpenAiAiService();
    const geoLocationService = new AdvancedGeoLocationService();
    const analyzeService = new OpenAiServiceV2(basePigeonRepo);

    // const analysis = await analyzeService.analyzePigeonPicture(
    //     "https://firebasestorage.googleapis.com/v0/b/pigeon-gogo.firebasestorage.app/o/shots%2F6e71e118-9888-4872-8623-f46d5cb31ed4%2Fstandard_12.png?alt=media&token=9f38c6bc-b59a-4574-a2ce-5e69bf51092d",
    // );
    // const analysis = await analyzeService.analyzePigeonPicture(
    //     "https://firebasestorage.googleapis.com/v0/b/pigeon-gogo.firebasestorage.app/o/shots%2F6e71e118-9888-4872-8623-f46d5cb31ed4%2Flow_quali_pidge2.png?alt=media&token=def188db-b5f3-4c8c-9ed2-7437cf2b8473",
    // );
    // const analysis = await analyzeService.analyzePigeonPicture(
    //     "https://firebasestorage.googleapis.com/v0/b/pigeon-gogo.firebasestorage.app/o/shots%2F6e71e118-9888-4872-8623-f46d5cb31ed4%2F1CE2C55A-FA59-4161-B269-038B3D410338_1_105_c.jpeg?alt=media&token=a02b7162-9932-4e6c-9a8c-e2742b0e390d",
    // );
    const analysis = await analyzeService.analyzePigeonPicture(
        "https://firebasestorage.googleapis.com/v0/b/pigeon-gogo.firebasestorage.app/o/shots%2F6e71e118-9888-4872-8623-f46d5cb31ed4%2FCapture%20d%E2%80%99e%CC%81cran%202025-05-10%20a%CC%80%2019.12.56.png?alt=media&token=97f4686b-fb6d-47a9-93c8-dfe2429c7840",
    );
    console.log("Analysis:", analysis);
    // const capturePigeonUseCase = new CapturePigeonUseCase(
    //     trainerRepo,
    //     pigeonRepo,
    //     basePigeonRepo,
    //     aiService,
    //     geoLocationService,
    // );

    try {
        // Test with coordinates (New York City) in the file path
        const latitude = 0.049;
        const longitude = 0.049;
        const filePath = `shots/6e71e118-9888-4872-8623-f46d5cb31ed4/${latitude}_${longitude}_test.jpg`;

        // await capturePigeonUseCase.execute(filePath);
        console.log(`Pigeon captured at coordinates: ${latitude}, ${longitude}`);
        console.log(
            `Pigeon gang: ${geoLocationService.determineGang({ latitude, longitude }, new Date(2025, 0, 1, 32, 0, 0, 0))}`,
        );
    } catch (error) {
        console.error("Error adding pigeon:", error);
    } finally {
        // Close the Firestore connection
        await admin.app().delete();
    }
}

testCapturePigeon();
