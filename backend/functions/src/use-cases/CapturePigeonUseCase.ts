import { v4 as uuidv4 } from "uuid";
import { DEFAULT_MAX_FATIGUE, Pigeon } from "../domain/entities/Pigeon";
import { PigeonRarity } from "../domain/enums/PigeonRarity";
import { PigeonRepository } from "../domain/repositories/PigeonRepository";
import { TrainerRepository } from "../domain/repositories/TrainerRepository";
import { AnalysisService } from "../domain/services/AnalysisService";
import { Coordinates, GeoLocationService } from "../domain/services/GeoLocationService";
import { defaultStats } from "../domain/value-objects/Stats";

export class CapturePigeonUseCase {
    constructor(
        private trainerRepo: TrainerRepository,
        private pigeonRepo: PigeonRepository,
        private analysisService: AnalysisService,
        private geoLocationService: GeoLocationService,
    ) {}

    async execute(storageFilePath: string): Promise<void> {
        // storageFilePath is like "shots/<trainerId>/<latitude>_<longitude>_<fileName>.jpg"
        // We need to extract the trainerId and coordinates from the path
        const pathParts = storageFilePath.split("/");
        const captureDate = new Date();
        if (pathParts.length < 3) {
            throw new Error("Invalid file path format");
        }

        const trainerId = pathParts[1];
        const trainer = await this.trainerRepo.getById(trainerId);

        if (!trainer) {
            throw new Error("Trainer not found");
        }

        if (trainer.pigeonBalls <= 0) {
            throw new Error("No capture stock available");
        }

        // Update trainer's pigeon ball count
        trainer.pigeonBalls -= 1;
        await this.trainerRepo.update(trainer.toDocument());

        try {
            // Extract coordinates from the filename
            // Format: <latitude>_<longitude>_<fileName>.jpg
            let coordinates: Coordinates = { latitude: 0, longitude: 0 };
            const fileNameParts = pathParts[2].split("_");
            if (fileNameParts.length >= 2) {
                const latitude = parseFloat(fileNameParts[0]);
                const longitude = parseFloat(fileNameParts[1]);
                if (!isNaN(latitude) && !isNaN(longitude)) {
                    coordinates = { latitude, longitude };
                }
            }

            // Analyze the pigeon picture and get ranked base pigeons
            const rankedPigeons = await this.analysisService.analyzePigeonPicture(storageFilePath);

            if (rankedPigeons.length === 0) {
                throw new Error("No matching pigeons found");
            }

            // Use the highest ranked pigeon
            const closestBasePigeon = rankedPigeons[0].basePigeon;
            console.info("Closest base pigeon:", closestBasePigeon.slug, "with score:", rankedPigeons[0].score);

            // Determine the gang based on geolocation
            const gang = this.geoLocationService.determineGang(coordinates, captureDate);

            const newPigeon = new Pigeon({
                id: uuidv4(),
                ownerId: trainerId,
                items: {
                    hat: null,
                    mount: null,
                    leftWing: null,
                    rightWing: null,
                },
                basePigeonId: closestBasePigeon.id,
                baseStats: defaultStats,
                currentFatigue: DEFAULT_MAX_FATIGUE,
                maxFatigue: DEFAULT_MAX_FATIGUE,
                capturedAt: captureDate,
                rarity: this._getRarity(),
                originalPicture: {
                    smallThumbnailUrl: storageFilePath,
                    originalUrl: storageFilePath,
                },
                level: 0,
                trickeryPoints: 0,
                auraPoints: 0,
                gang: gang,
            });

            await this.pigeonRepo.save(newPigeon.toDocument());
        } catch (error) {
            console.error("Error capturing pigeon:", error);
            // cancel trainer pigeon ball
            trainer.pigeonBalls += 1;
            await this.trainerRepo.update(trainer.toDocument());
            throw error;
        }
    }

    private _getRarity(): PigeonRarity {
        const random = Math.random();
        if (random < 0.01) {
            return PigeonRarity.LEGENDARY;
        } else if (random < 0.1) {
            return PigeonRarity.EPIC;
        } else if (random < 0.3) {
            return PigeonRarity.RARE;
        } else {
            return PigeonRarity.COMMON;
        }
    }
}
