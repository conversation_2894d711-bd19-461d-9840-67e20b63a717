import { PigeonRepository } from "../domain/repositories/PigeonRepository";
import { TrainerRepository } from "../domain/repositories/TrainerRepository";
import { PigeonDocument } from "../domain/entities/Pigeon";

export class GetTrainerPigeonsUseCase {
    constructor(
        private trainerRepo: TrainerRepository,
        private pigeonRepo: PigeonRepository,
    ) {}

    async execute(trainerId: string, limit = 40, offset = 0): Promise<{ pigeons: PigeonDocument[] }> {
        const trainer = await this.trainerRepo.getById(trainerId);
        if (!trainer) {
            throw new Error("Trainer not found");
        }
        const pigeonDocuments = await this.pigeonRepo.getByOwnerId(trainerId);

        return {
            pigeons: pigeonDocuments,
        };
    }
}
