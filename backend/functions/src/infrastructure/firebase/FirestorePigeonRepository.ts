import * as admin from "firebase-admin";
import { PigeonRepository } from "../../domain/repositories/PigeonRepository";
import { PigeonDocument } from "../../domain/entities/Pigeon";

export class FirestorePigeonRepository implements PigeonRepository {
    private collection = admin.firestore().collection("pigeons");

    async save(pigeon: PigeonDocument): Promise<void> {
        await this.collection.doc(pigeon.id).set(pigeon);
    }

    async getByOwnerId(ownerId: string): Promise<PigeonDocument[]> {
        const pigeons: PigeonDocument[] = [];
        const snapshot = await this.collection.where("ownerId", "==", ownerId).get();

        snapshot.forEach((doc) => {
            const data = doc.data();
            const pigeon = {
                ...data,
                capturedAt:
                    data.capturedAt instanceof admin.firestore.Timestamp ? data.capturedAt.toDate() : data.capturedAt,
            } as PigeonDocument;

            pigeons.push(pigeon);
        });

        return pigeons;
    }
}
