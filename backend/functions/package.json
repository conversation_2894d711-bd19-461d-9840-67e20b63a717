{"name": "functions", "scripts": {"lint": "eslint --ext .js,.ts . && prettier --check .", "format": "prettier --write .", "build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "test": "jest -c jest.config.js"}, "engines": {"node": "22"}, "main": "lib/index.js", "dependencies": {"@google-cloud/functions-framework": "^3.4.6", "dotenv": "^16.4.7", "firebase-admin": "^12.1.0", "firebase-functions": "^6.3.2", "lodash": "^4.17.21", "openai": "^4.90.0", "uuid": "^11.1.0"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^22.13.14", "@typescript-eslint/eslint-plugin": "^5.12.0", "@typescript-eslint/parser": "^5.12.0", "eslint": "^8.9.0", "eslint-config-google": "^0.14.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-import": "^2.25.4", "eslint-plugin-prettier": "^5.2.6", "firebase-functions-test": "^3.1.0", "prettier": "^3.5.3", "ts-jest": "^29.2.6", "ts-node": "^10.9.2", "typescript": "^4.9.5"}, "private": true}